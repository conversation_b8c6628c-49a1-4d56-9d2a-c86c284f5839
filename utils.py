#!/usr/bin/env python3
"""
Simple Utils module for Keno Predictor
Only returns id and period_id from API
"""

import requests
import json
from datetime import datetime, timedelta
import time
import os
import pandas as pd
import joblib
import numpy as np

# Simple cache implementation using file system
CACHE_DIR = "cache"
CACHE_DURATION = 35  # seconds

def ensure_cache_dir():
    """Ensure cache directory exists"""
    if not os.path.exists(CACHE_DIR):
        os.makedirs(CACHE_DIR)

def get_cache(cache_key):
    """Get cached data if it exists and is not expired"""
    ensure_cache_dir()
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")
    
    if not os.path.exists(cache_file):
        return None
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        # Check if cache is expired
        cache_time = cache_data.get('timestamp', 0)
        if time.time() - cache_time > CACHE_DURATION:
            os.remove(cache_file)
            return None
        
        return cache_data.get('data')
    except:
        return None

def put_cache(cache_key, data):
    """Store data in cache with timestamp"""
    ensure_cache_dir()
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")
    
    cache_data = {
        'timestamp': time.time(),
        'data': data
    }
    
    try:
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f)
    except:
        pass  # Ignore cache write errors

def current_list_result_keno(day=1):
    """
    Get current list of Keno results from API
    
    Args:
        day (int): Number of days to add to current date (default: 1)
    
    Returns:
        dict or bool: API response data or True if before 6 AM
    """
    # Get current date and add days
    now = datetime.now()
    from_date = now.strftime('%Y/%m/%d')
    to_date = (now + timedelta(days=day)).strftime('%Y/%m/%d')
    
    # Build API URL
    url = (f'https://api-knlt.gamingon.net/api/v1/rounds/1'
           f'?limit=3&status=ENDED&sort=DESC&page=0'
           f'&from_date={from_date}&to_date={to_date}')
    
    # Headers
    headers = {
        'Content-Type': 'application/json'
    }
    
    # Cache key
    cache_key = 'currentListResultKenno'
    
    # Try to get from cache first
    response = get_cache(cache_key)
    
    if response is None:
        try:
            # Make API request
            api_response = requests.get(url, headers=headers, timeout=10)
            api_response.raise_for_status()
            
            response_data = api_response.json()
            
            # Check if it's before 6 AM (early morning)
            current_hour = now.hour
            if current_hour < 6:
                return True
            
            # Get first item from content
            if 'content' in response_data and len(response_data['content']) > 0:
                response = response_data['content'][0]
                
                # Cache the response
                put_cache(cache_key, response)
            else:
                return None
                
        except:
            return None
    
    return response

def get_keno_id_and_period():
    """
    Get latest Keno id and period_id only

    Returns:
        dict: {"id": 198740, "period_id": "0240261"} or error status
    """
    raw_data = current_list_result_keno()

    if raw_data is True:
        return {"status": "before_6am"}
    elif raw_data is None:
        return {"status": "error"}

    return {
        "id": raw_data.get("id"),
        "period_id": raw_data.get("periodId")
    }

def place_keno_bet(token, bet_type, amount, round_id, vietlott_ticket, bet_numbers):
    """
    Place a Keno bet via API

    Args:
        token (str): Authentication token (e.g., "5-2ab87cad83f647e05a98cde380a072d2")
        bet_type (str): Type of bet (e.g., "TRUOT_XIEN_4")
        amount (int): Bet amount (e.g., 10)
        round_id (int): Round ID (e.g., 166990)
        vietlott_ticket (str): Vietlott ticket number (e.g., "0208511")
        bet_numbers (str): Comma-separated bet numbers (e.g., "33,12,21,31")

    Returns:
        dict: API response or error status
    """
    url = 'https://api-knlt.gamingon.net/api/v1/bet'

    headers = {
        'Content-Type': 'application/json'
    }

    payload = {
        "token": token,
        "betType": bet_type,
        "amount": amount,
        "roundId": round_id,
        "vietlottTicket": vietlott_ticket,
        "betNumbers": bet_numbers
    }

    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=10,
            allow_redirects=True
        )
        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": f"Request failed: {e}"}
    except json.JSONDecodeError as e:
        return {"status": "error", "message": f"JSON decode error: {e}"}
    except Exception as e:
        return {"status": "error", "message": f"Unexpected error: {e}"}

def test_simple():
    """Test function - only shows id and period_id"""
    print("🧪 TESTING SIMPLE KENO API")
    print("="*50)

    # Show URL
    now = datetime.now()
    from_date = now.strftime('%Y/%m/%d')
    to_date = (now + timedelta(days=1)).strftime('%Y/%m/%d')
    url = (f'https://api-knlt.gamingon.net/api/v1/rounds/1'
           f'?limit=3&status=ENDED&sort=DESC&page=0'
           f'&from_date={from_date}&to_date={to_date}')

    print(f"🌐 API URL: {url}")
    print("="*50)

    # Test function
    result = get_keno_id_and_period()

    if "status" in result:
        print(f"📊 Status: {result.get('status')}")
    else:
        print(f"🆔 Record ID: {result.get('id')}")
        print(f"🎯 Period ID: {result.get('period_id')}")

    print("="*50)

def test_bet_function():
    """Test bet function with sample data (won't actually place bet)"""
    print("\n🧪 TESTING BET FUNCTION")
    print("="*50)

    # Sample data from PHP example
    sample_data = {
        "token": "5-2ab87cad83f647e05a98cde380a072d2",
        "bet_type": "TRUOT_XIEN_4",
        "amount": 10,
        "round_id": 166990,
        "vietlott_ticket": "0208511",
        "bet_numbers": "33,12,21,31"
    }

    print("📝 Sample bet data:")
    for key, value in sample_data.items():
        print(f"   {key}: {value}")

    print("\n⚠️ NOTE: This is just a function test, not placing actual bet")
    print("🌐 Bet URL: https://api-knlt.gamingon.net/api/v1/bet")

    # Show what the payload would look like
    payload = {
        "token": sample_data["token"],
        "betType": sample_data["bet_type"],
        "amount": sample_data["amount"],
        "roundId": sample_data["round_id"],
        "vietlottTicket": sample_data["vietlott_ticket"],
        "betNumbers": sample_data["bet_numbers"]
    }

    print(f"\n📦 JSON Payload:")
    print(json.dumps(payload, indent=2))

    print("="*50)

def extract_features(draws_70):
    """Tạo DataFrame 1 dòng, 240 cột (80 số × 3 đặc trưng).

    * rolling_miss_i   = tỉ lệ trượt của số i trong 3 kỳ cuối.
    * decay_miss_i     = Σ trượt * e^(−0.5·k) (k tính từ kỳ gần nhất).
    * acceleration_i   = đạo hàm bậc 2 trên chuỗi miss‑rate 3‑kỳ.
    """
    if len(draws_70) < 3:
        raise ValueError("draws_70 must contain ≥ 3 rounds")

    all_nums = range(1, 81)
    rounds = len(draws_70)

    # ma trận 0/1: 1 = trượt, 0 = trúng
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws_70):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0

    feat_rows = {}
    for idx, num in enumerate(all_nums):
        col = miss_mat[:, idx]           # vector 0/1 độ dài rounds

        # --- rolling miss 3 kỳ cuối ---
        rolling_miss = col[-3:].mean()

        # --- decay weighted miss (kỳ mới quan trọng hơn) ---
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)

        # --- miss acceleration (đạo hàm bậc 2) ---
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2

        feat_rows[f"rolling_miss_{num}"] = rolling_miss
        feat_rows[f"decay_miss_{num}"] = decay_miss
        feat_rows[f"acceleration_{num}"] = acceleration

    return pd.DataFrame([feat_rows])

def predict_10_missing_numbers(day_draws):
    """
    Dự đoán 10 số có khả năng trượt cao nhất

    Args:
        day_draws (list): Mảng kết quả các kì trong ngày
                         Format: [[1,2,3,...], [2,3,4....],....]

    Returns:
        list: Top 10 số có khả năng trượt cao nhất
    """
    try:
        # Kiểm tra input
        if not day_draws or len(day_draws) < 30:
            raise ValueError("Input must have at least 30 rounds.")

        # Load trained models
        models = joblib.load("multi_label_models.pkl")

        # Convert input to features
        df_feat = extract_features(day_draws)
        X = df_feat.iloc[:, :240]  # Only 240 features (80 x 3), no labels

        # Predict probability of miss (label = 1 means miss)
        preds = {}
        for label, model in models.items():
            prob = model.predict_proba(X)[:, 1][0]  # single sample
            index = int(label.split("_")[-1])  # label_23 -> 23
            preds[index] = prob

        # Sort by highest miss prob
        top10 = sorted(preds.items(), key=lambda x: -x[1])[:10]
        top10_numbers = [i[0] for i in top10]

        return top10_numbers

    except Exception as e:
        print(f"❌ Error in predict_10_missing_numbers: {e}")
        return []

def predict_6_winning_numbers(day_draws):
    """
    Dự đoán 6 số có khả năng THẮNG cao nhất (ngược lại với missing)

    Args:
        day_draws (list): Mảng kết quả các kì trong ngày
                         Format: [[1,2,3,...], [2,3,4....],....]

    Returns:
        list: Top 6 số có khả năng thắng cao nhất
    """
    try:
        # Lấy 10 số có khả năng trượt cao nhất
        top10_missing = predict_10_missing_numbers(day_draws)

        if not top10_missing:
            return []

        # Lấy tất cả số từ 1-80 trừ đi 10 số trượt
        all_numbers = set(range(1, 81))
        remaining_numbers = list(all_numbers - set(top10_missing))

        # Tính tần suất xuất hiện của các số còn lại trong dữ liệu gần đây
        from collections import Counter
        recent_periods = day_draws[-10:] if len(day_draws) >= 10 else day_draws

        frequency = Counter()
        for period in recent_periods:
            for num in period:
                if num in remaining_numbers:
                    frequency[num] += 1

        # Sắp xếp theo tần suất giảm dần và lấy top 6
        most_frequent = sorted(frequency.items(), key=lambda x: -x[1])
        top6_winning = [num for num, _ in most_frequent[:6]]

        # Nếu không đủ 6 số, bổ sung từ danh sách còn lại
        if len(top6_winning) < 6:
            needed = 6 - len(top6_winning)
            used_numbers = set(top6_winning)
            additional = [num for num in remaining_numbers if num not in used_numbers][:needed]
            top6_winning.extend(additional)

        return top6_winning[:6]

    except Exception as e:
        print(f"❌ Error in predict_6_winning_numbers: {e}")
        return []

def test_predict_10_missing():
    """Test function for predict_10_missing_numbers"""
    print("\n🧪 TESTING PREDICT 10 MISSING NUMBERS")
    print("="*50)

    # Sample data - 30 periods with random keno results
    sample_draws = []
    for i in range(30):
        # Generate random 20 numbers from 1-80 for each period
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        sample_draws.append(period_result)

    print(f"📊 Sample input: {len(sample_draws)} periods")
    print(f"📝 First period: {sample_draws[0]}")
    print(f"📝 Last period: {sample_draws[-1]}")

    try:
        # Test prediction
        top10_missing = predict_10_missing_numbers(sample_draws)

        if top10_missing:
            print(f"\n🎯 Top 10 missing numbers: {top10_missing}")
            print(f"✅ Prediction successful!")
        else:
            print("❌ Prediction failed - empty result")

    except Exception as e:
        print(f"❌ Test failed: {e}")

    print("="*50)

def test_predict_6_winning():
    """Test function for predict_6_winning_numbers"""
    print("\n🧪 TESTING PREDICT 6 WINNING NUMBERS")
    print("="*50)

    # Sample data - 30 periods with random keno results
    sample_draws = []
    for i in range(30):
        # Generate random 20 numbers from 1-80 for each period
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        sample_draws.append(period_result)

    print(f"📊 Sample input: {len(sample_draws)} periods")
    print(f"📝 First period: {sample_draws[0]}")
    print(f"📝 Last period: {sample_draws[-1]}")

    try:
        # Test prediction
        top6_winning = predict_6_winning_numbers(sample_draws)

        if top6_winning:
            print(f"\n🎯 Top 6 winning numbers: {top6_winning}")
            print(f"✅ Prediction successful!")
        else:
            print("❌ Prediction failed - empty result")

    except Exception as e:
        print(f"❌ Test failed: {e}")

    print("="*50)

def test_predict_with_real_data():
    """Test với dữ liệu thực từ database"""
    print("\n🧪 TESTING WITH REAL DATABASE DATA")
    print("="*50)

    try:
        import mysql.connector

        # Kết nối database
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='histories_keno'
        )
        cursor = conn.cursor(dictionary=True)

        # Lấy dữ liệu 1 ngày gần đây
        query = """
        SELECT results FROM keno_results
        WHERE date = '2025-06-21'
        ORDER BY period_id ASC
        LIMIT 50
        """

        cursor.execute(query)
        rows = cursor.fetchall()

        if len(rows) < 30:
            print("❌ Không đủ dữ liệu (cần ít nhất 30 kì)")
            return

        # Chuyển đổi dữ liệu
        day_draws = []
        for row in rows:
            if isinstance(row['results'], str):
                results = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
            else:
                results = row['results']
            day_draws.append(results)

        print(f"📊 Real data: {len(day_draws)} periods from 2025-06-21")
        print(f"📝 First period: {day_draws[0]}")
        print(f"📝 Last period: {day_draws[-1]}")

        # Test với 30 kì đầu để dự đoán kì 31
        training_data = day_draws[:30]
        actual_result = day_draws[30] if len(day_draws) > 30 else None

        # Dự đoán
        top10_missing = predict_10_missing_numbers(training_data)

        if top10_missing and actual_result:
            print(f"\n🎯 Top 10 predicted missing: {top10_missing}")
            print(f"🎲 Actual result period 31: {actual_result}")

            # Tính hit rate
            actual_missing = [i for i in range(1, 81) if i not in actual_result]
            hits = len([num for num in top10_missing if num in actual_missing])
            hit_rate = hits / 10 * 100

            print(f"📊 Hit rate: {hits}/10 ({hit_rate:.1f}%)")
            print(f"✅ Test completed!")
        else:
            print(f"🎯 Top 10 predicted missing: {top10_missing}")
            print("⚠️ No actual result to compare")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ Test failed: {e}")

    print("="*50)

def demo_predict_usage():
    """Demo cách sử dụng hàm predict_10_missing_numbers"""
    print("\n📖 DEMO USAGE - PREDICT 10 MISSING NUMBERS")
    print("="*60)

    print("🔧 Cách sử dụng:")
    print("from utils import predict_10_missing_numbers, predict_6_winning_numbers")
    print("")
    print("# Input: mảng kết quả các kì trong ngày")
    print("day_draws = [")
    print("    [1, 5, 12, 18, 23, 34, 45, 56, 67, 78, ...],  # Kì 1")
    print("    [2, 8, 15, 22, 29, 36, 43, 50, 61, 72, ...],  # Kì 2")
    print("    [3, 9, 16, 25, 32, 39, 46, 53, 64, 75, ...],  # Kì 3")
    print("    # ... ít nhất 30 kì")
    print("]")
    print("")
    print("# Dự đoán 10 số có khả năng trượt cao nhất")
    print("top10_missing = predict_10_missing_numbers(day_draws)")
    print("print(f'Top 10 missing: {top10_missing}')")
    print("")
    print("# Dự đoán 6 số có khả năng thắng cao nhất")
    print("top6_winning = predict_6_winning_numbers(day_draws)")
    print("print(f'Top 6 winning: {top6_winning}')")
    print("")
    print("📋 Yêu cầu:")
    print("- Input: ít nhất 30 kì")
    print("- Mỗi kì: list các số từ 1-80")
    print("- Output missing: list 10 số có khả năng trượt cao nhất")
    print("- Output winning: list 6 số có khả năng thắng cao nhất")
    print("- Cần file: multi_label_models.pkl")

    print("="*60)

if __name__ == "__main__":
    test_simple()
    test_bet_function()
    test_predict_10_missing()
    test_predict_6_winning()
    demo_predict_usage()
    # test_predict_with_real_data()  # Uncomment when database is available
