#!/usr/bin/env python3
"""
Test script để kiểm tra v3.py với AI model
"""

from v3 import FinalKenoPredictor
import random

def test_ai_predictions():
    print("🧪 TEST V3.PY VỚI AI MODEL")
    print("="*50)
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu mẫu - 35 kì
    day_results = []
    for i in range(35):
        # Mỗi kì có 20 số từ 1-80
        period_result = sorted(random.sample(range(1, 81), 20))
        day_results.append({'results': period_result})
    
    print(f"📊 Dữ liệu test: {len(day_results)} kì")
    print(f"📝 Kì đầu: {day_results[0]['results']}")
    print(f"📝 Kì cuối: {day_results[-1]['results']}")
    print()
    
    # Test AI predictions
    print("🤖 Test AI predictions...")
    try:
        ai_predictions = predictor.get_ai_predictions(day_results, 10)
        print(f"✅ AI predictions: {ai_predictions}")
        
        # Test ensemble predictions
        print("\n🎯 Test ensemble predictions...")
        final_6, confidence, predictions_dict = predictor.get_ensemble_predictions(day_results)
        print(f"✅ Final 6: {final_6}")
        print(f"✅ Confidence: {confidence}")
        print(f"✅ Predictions dict: {predictions_dict}")
        
        # Test combinations
        if len(final_6) >= 4:
            print(f"\n🎲 Test combinations từ 6 số...")
            combos = predictor.generate_smart_combinations(final_6, 15)
            print(f"✅ Generated {len(combos)} combinations:")
            for i, combo in enumerate(combos[:5]):  # Show first 5
                print(f"   Combo {i+1}: {combo}")
            if len(combos) > 5:
                print(f"   ... và {len(combos)-5} combinations khác")
        
        print(f"\n✅ Test thành công!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("="*50)

if __name__ == "__main__":
    test_ai_predictions()
